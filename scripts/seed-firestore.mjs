// Seed Firestore Emulator with example data
// Usage:
//   npx firebase emulators:exec --only firestore --project daily-angel "node scripts/seed-firestore.mjs"

import { initializeApp } from 'firebase-admin/app';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';

// Ensure we connect to the emulator if available
process.env.FIRESTORE_EMULATOR_HOST = process.env.FIRESTORE_EMULATOR_HOST || '127.0.0.1:8080';

// Initialize Admin SDK with just projectId (sufficient for emulators)
initializeApp({ projectId: 'daily-angel' });
const db = getFirestore();

const now = Timestamp.now();

async function seed() {
  console.log('Seeding Firestore emulator...');

  // Categories
  const categories = [
    { id: 'education', name: 'Education', description: 'Educational causes and initiatives', color: '#FF6B6B', icon: 'https://example.com/icons/edu.svg', isActive: true, sortOrder: 1, createdAt: now },
    { id: 'health', name: 'Health', description: 'Health and wellness initiatives', color: '#4CAF50', icon: 'https://example.com/icons/health.svg', isActive: true, sortOrder: 2, createdAt: now },
    { id: 'environment', name: 'Environment', description: 'Environmental causes', color: '#2196F3', icon: 'https://example.com/icons/env.svg', isActive: true, sortOrder: 3, createdAt: now }
  ];
  for (const c of categories) {
    await db.collection('categories').doc(c.id).set(c);
  }

  // Users
  const users = [
    {
      id: 'uid_john',
      uid: 'uid_john',
      email: '<EMAIL>',
      displayName: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: now,
      gender: 'male',
      location: { country: 'USA', state: 'CA', city: 'San Francisco' },
      userType: 'individual',
      interests: ['education', 'environment'],
      preferredLanguage: 'en',
      totalDonated: 0,
      donationCount: 0,
      favoriteNGOs: [],
      followedCauses: [],
      notificationSettings: { email: true, push: true, sms: false, updates: true, campaigns: true },
      createdAt: now,
      updatedAt: now,
      isActive: true,
      isVerified: false
    },
    {
      id: 'uid_jane',
      uid: 'uid_jane',
      email: '<EMAIL>',
      displayName: 'Jane Smith',
      firstName: 'Jane',
      lastName: 'Smith',
      dateOfBirth: now,
      gender: 'female',
      location: { country: 'USA', state: 'NY', city: 'New York' },
      userType: 'individual',
      interests: ['health'],
      preferredLanguage: 'en',
      totalDonated: 0,
      donationCount: 0,
      favoriteNGOs: [],
      followedCauses: [],
      notificationSettings: { email: true, push: true, sms: false, updates: true, campaigns: true },
      createdAt: now,
      updatedAt: now,
      isActive: true,
      isVerified: false
    }
  ];
  for (const u of users) {
    await db.collection('users').doc(u.id).set(u);
  }

  // NGOs
  const ngos = [
    {
      id: 'ngo_save_children',
      name: 'Save the Children',
      description: 'Long description of NGO mission...',
      shortDescription: 'Brief mission statement',
      registrationNumber: 'REG123456',
      taxId: 'TAX789012',
      legalStatus: '501c3',
      registrationCountry: 'USA',
      email: '<EMAIL>',
      phone: '+**********',
      website: 'https://savechildren.org',
      address: { street: '123 Main St', city: 'New York', state: 'NY', country: 'USA', zipCode: '10001' },
      logo: 'https://example.com/logo1.png',
      coverImage: 'https://example.com/cover1.jpg',
      images: [],
      primaryCategory: 'education',
      categories: ['education', 'children', 'poverty'],
      focusAreas: ['K-12 Education', 'School Supplies', 'Teacher Training'],
      targetDemographic: 'children',
      totalReceived: 0,
      donorCount: 0,
      averageDonation: 0,
      isVerified: true,
      verificationDate: now,
      trustScore: 75,
      transparency: { financialReportsAvailable: true, lastAuditDate: now, impactReportsAvailable: true },
      socialMedia: { facebook: 'https://facebook.com/ngo1' },
      isActive: true,
      acceptsDonations: true,
      minimumDonation: 5,
      adminUsers: ['<EMAIL>', '<EMAIL>'],
      createdAt: now,
      updatedAt: now,
      createdBy: 'uid_john'
    },
    {
      id: 'ngo_green_earth',
      name: 'Green Earth',
      description: 'Environmental protection initiatives',
      shortDescription: 'Protecting our planet',
      registrationNumber: 'REG654321',
      taxId: 'TAX210987',
      legalStatus: 'charity',
      registrationCountry: 'USA',
      email: '<EMAIL>',
      phone: '+1987654321',
      website: 'https://greenearth.org',
      address: { street: '456 Park Ave', city: 'San Francisco', state: 'CA', country: 'USA', zipCode: '94105' },
      logo: 'https://example.com/logo2.png',
      coverImage: 'https://example.com/cover2.jpg',
      images: [],
      primaryCategory: 'environment',
      categories: ['environment'],
      focusAreas: ['Reforestation', 'Clean Air'],
      targetDemographic: 'all',
      totalReceived: 0,
      donorCount: 0,
      averageDonation: 0,
      isVerified: false,
      trustScore: 60,
      transparency: { financialReportsAvailable: false, impactReportsAvailable: true },
      socialMedia: { twitter: 'https://twitter.com/green_earth' },
      isActive: true,
      acceptsDonations: true,
      minimumDonation: 5,
      adminUsers: ['<EMAIL>'],
      createdAt: now,
      updatedAt: now,
      createdBy: 'uid_jane'
    }
  ];
  for (const n of ngos) {
    await db.collection('ngos').doc(n.id).set(n);
    // Subcollections
    await db.collection('ngos').doc(n.id).collection('team_members').doc('member1').set({
      userId: 'uid_john',
      role: 'director',
      joinedAt: now
    });
  }

  // Companies
  const companies = [
    {
      id: 'company_google',
      name: 'Google LLC',
      description: 'Technology company focused on...',
      industry: 'Technology',
      size: 'large',
      registrationNumber: 'COMP123456',
      taxId: 'TAX345678',
      legalStructure: 'LLC',
      email: '<EMAIL>',
      phone: '+14085551234',
      website: 'https://google.com',
      headquarters: { street: '1600 Amphitheatre Parkway', city: 'Mountain View', state: 'CA', country: 'USA', zipCode: '94043' },
      logo: 'https://example.com/google.png',
      coverImage: 'https://example.com/google-cover.jpg',
      csrFocus: ['education', 'environment', 'technology'],
      annualCSRBudget: 1000000,
      employeeCount: 50000,
      totalDonated: 0,
      campaignsSupported: 0,
      ngoPartnerships: ['ngo_save_children'],
      isVerified: true,
      verificationDate: now,
      isActive: true,
      adminUsers: ['<EMAIL>'],
      createdAt: now,
      updatedAt: now
    }
  ];
  for (const c of companies) {
    await db.collection('companies').doc(c.id).set(c);
  }

  // Causes
  const causes = [
    {
      id: 'cause_africa_schools',
      title: 'Build Schools in Rural Africa',
      description: 'Detailed description of the cause...',
      shortDescription: 'Brief cause description',
      createdBy: 'ngo_save_children',
      createdByType: 'ngo',
      category: 'education',
      subcategory: 'school_infrastructure',
      tags: ['africa', 'rural', 'schools', 'children'],
      goalAmount: 50000,
      raisedAmount: 0,
      donorCount: 0,
      startDate: now,
      endDate: now,
      duration: 90,
      featuredImage: 'https://example.com/africa.jpg',
      images: [],
      video: null,
      location: { country: 'Kenya', state: 'Nakuru', city: 'Nakuru', coordinates: { latitude: -0.303, longitude: 36.08 } },
      expectedImpact: 'Will provide education to 500 children',
      impactMetrics: [{ metric: 'children_educated', target: 500, current: 0 }],
      status: 'active',
      isUrgent: false,
      isFeatured: true,
      lastUpdateDate: now,
      updateCount: 0,
      createdAt: now,
      updatedAt: now,
      views: 0,
      shares: 0
    }
  ];
  for (const c of causes) {
    await db.collection('causes').doc(c.id).set(c);
    await db.collection('causes').doc(c.id).collection('comments').doc().set({
      authorId: 'uid_john',
      content: 'Amazing initiative! Happy to support.',
      createdAt: now
    });
    await db.collection('causes').doc(c.id).collection('donors').doc('uid_john').set({ joinedAt: now });
  }

  // Donations
  const donations = [
    {
      id: 'don_1',
      amount: 100,
      currency: 'USD',
      donorId: 'uid_john',
      donorType: 'user',
      recipientId: 'ngo_save_children',
      recipientType: 'ngo',
      causeId: 'cause_africa_schools',
      paymentMethod: 'credit_card',
      paymentProvider: 'stripe',
      paymentId: 'pi_test_1',
      status: 'completed',
      isRecurring: false,
      recurringFrequency: null,
      recurringEndDate: null,
      isAnonymous: false,
      message: 'Keep up the great work!',
      createdAt: now,
      processedAt: now,
      taxReceiptRequested: true,
      taxReceiptSent: false,
      taxReceiptUrl: null,
      impactUpdatesEnabled: true
    }
  ];
  for (const d of donations) {
    await db.collection('donations').doc(d.id).set(d);
  }

  // Campaigns
  const campaigns = [
    {
      id: 'camp_holiday_2025',
      title: 'Holiday Giving Campaign',
      description: 'Annual holiday giving campaign...',
      organizerId: 'company_google',
      organizerType: 'company',
      participatingNGOs: ['ngo_save_children'],
      supportingCompanies: ['company_google'],
      fundraisingGoal: 100000,
      totalRaised: 0,
      participantGoal: 50,
      currentParticipants: 0,
      startDate: now,
      endDate: now,
      bannerImage: 'https://example.com/banner.jpg',
      images: [],
      minimumDonation: 10,
      maximumDonation: null,
      allowAnonymous: true,
      status: 'active',
      createdAt: now,
      updatedAt: now
    }
  ];
  for (const c of campaigns) {
    await db.collection('campaigns').doc(c.id).set(c);
  }

  // Global updates
  const updates = [
    {
      id: 'update_1',
      entityId: 'cause_africa_schools',
      entityType: 'cause',
      title: 'Project Update: First School Completed!',
      content: 'Detailed update content...',
      images: [],
      video: null,
      authorId: 'ngo_save_children',
      authorType: 'ngo',
      authorName: 'Save the Children',
      impactMetrics: [{ metric: 'children_educated', previous: 0, current: 150, change: 150 }],
      likes: 0,
      comments: 0,
      shares: 0,
      createdAt: now,
      isPublic: true
    }
  ];
  for (const u of updates) {
    await db.collection('updates').doc(u.id).set(u);
  }

  console.log('Seed completed.');
}

seed().catch((err) => {
  console.error('Seed failed:', err);
  process.exit(1);
});




