rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    function signedIn() {
      return request.auth != null;
    }

    function userIs(userId) {
      return signedIn() && request.auth.uid == userId;
    }

    function requesterEmail() {
      return request.auth != null && request.auth.token.email != null
        ? request.auth.token.email
        : '';
    }

    function isEmailIn(list) {
      return list != null && requesterEmail() in list;
    }

    function isNgoAdmin(ngoId) {
      return isEmailIn(get(/databases/$(database)/documents/ngos/$(ngoId)).data.adminUsers);
    }

    function isCompanyAdmin(companyId) {
      return isEmailIn(get(/databases/$(database)/documents/companies/$(companyId)).data.adminUsers);
    }

    function causeOwnerCanWrite(causeId) {
      let cause = get(/databases/$(database)/documents/causes/$(causeId)).data;
      return (cause.createdByType == 'ngo' && isNgoAdmin(cause.createdBy)) ||
             (cause.createdByType == 'user' && userIs(cause.createdBy)) ||
             (cause.createdByType == 'company' && isCompanyAdmin(cause.createdBy));
    }

    function isCampaignOrganizer(campaignId) {
      let c = get(/databases/$(database)/documents/campaigns/$(campaignId)).data;
      return (c.organizerType == 'company' && isCompanyAdmin(c.organizerId)) ||
             (c.organizerType == 'ngo' && isNgoAdmin(c.organizerId));
    }

    // users: only the owner can read/write
    match /users/{userId} {
      allow read, create, update, delete: if userIs(userId);

      match /donation_history/{donationId} {
        allow read: if userIs(userId);
        allow create, update, delete: if false; // managed by system
      }

      match /notifications/{notificationId} {
        allow read: if userIs(userId);
        allow create, update, delete: if userIs(userId);
      }

      match /saved_causes/{causeId} {
        allow read, write: if userIs(userId);
      }
    }

    // ngos: public read; ngo admins write
    match /ngos/{ngoId} {
      allow read: if true;
      allow create: if signedIn() && isEmailIn(request.resource.data.adminUsers);
      allow update, delete: if signedIn() && isNgoAdmin(ngoId);

      match /team_members/{memberId} {
        allow read: if true;
        allow write: if signedIn() && isNgoAdmin(ngoId);
      }

      match /financial_reports/{reportId} {
        allow read: if true;
        allow write: if signedIn() && isNgoAdmin(ngoId);
      }

      match /impact_reports/{reportId} {
        allow read: if true;
        allow write: if signedIn() && isNgoAdmin(ngoId);
      }
    }

    // companies: public read; company admins write
    match /companies/{companyId} {
      allow read: if true;
      allow create: if signedIn() && isEmailIn(request.resource.data.adminUsers);
      allow update, delete: if signedIn() && isCompanyAdmin(companyId);
    }

    // causes: public read; owner writes (ngo admin, company admin, or user)
    match /causes/{causeId} {
      allow read: if true;
      allow create: if signedIn() && (
        (request.resource.data.createdByType == 'ngo' && isNgoAdmin(request.resource.data.createdBy)) ||
        (request.resource.data.createdByType == 'user' && userIs(request.resource.data.createdBy)) ||
        (request.resource.data.createdByType == 'company' && isCompanyAdmin(request.resource.data.createdBy))
      );
      allow update, delete: if signedIn() && causeOwnerCanWrite(causeId);

      match /updates/{updateId} {
        allow read: if true;
        allow write: if signedIn() && causeOwnerCanWrite(causeId);
      }

      match /donors/{donorId} {
        allow read: if true; // adjust if stricter privacy is required
        allow write: if signedIn() && causeOwnerCanWrite(causeId);
      }

      match /comments/{commentId} {
        allow read: if true;
        allow create: if signedIn();
        allow update, delete: if signedIn() && (
          causeOwnerCanWrite(causeId) || request.auth.uid == resource.data.authorId
        );
      }
    }

    // donations: donor or recipient can read; donor creates
    match /donations/{donationId} {
      allow create: if signedIn() && request.auth.uid == request.resource.data.donorId;
      allow read: if signedIn() && (
        request.auth.uid == resource.data.donorId ||
        (resource.data.recipientType == 'ngo' && isNgoAdmin(resource.data.recipientId)) ||
        (resource.data.recipientType == 'cause' && causeOwnerCanWrite(resource.data.causeId))
      );
      allow update: if signedIn() && (
        (request.auth.uid == resource.data.donorId && resource.data.status == 'pending') ||
        (resource.data.recipientType == 'ngo' && isNgoAdmin(resource.data.recipientId)) ||
        (resource.data.recipientType == 'cause' && causeOwnerCanWrite(resource.data.causeId))
      );
      allow delete: if false;
    }

    // campaigns: public read; organizer writes
    match /campaigns/{campaignId} {
      allow read: if true;
      allow create: if signedIn() && (
        (request.resource.data.organizerType == 'company' && isCompanyAdmin(request.resource.data.organizerId)) ||
        (request.resource.data.organizerType == 'ngo' && isNgoAdmin(request.resource.data.organizerId))
      );
      allow update, delete: if signedIn() && isCampaignOrganizer(campaignId);
    }

    // categories: public read; platform admins write
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if signedIn() && request.auth.token.admin == true;
    }

    // global updates: public read; author writes
    match /updates/{updateId} {
      allow read: if true;
      allow create: if signedIn() && (
        (request.resource.data.authorType == 'ngo' && isNgoAdmin(request.resource.data.authorId)) ||
        (request.resource.data.authorType == 'company' && isCompanyAdmin(request.resource.data.authorId)) ||
        (request.resource.data.authorType == 'user' && userIs(request.resource.data.authorId))
      );
      allow update, delete: if signedIn() && (
        (resource.data.authorType == 'ngo' && isNgoAdmin(resource.data.authorId)) ||
        (resource.data.authorType == 'company' && isCompanyAdmin(resource.data.authorId)) ||
        (resource.data.authorType == 'user' && userIs(resource.data.authorId))
      );
    }
  }
}
