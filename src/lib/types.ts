import { Timestamp } from "firebase/firestore";

// Shared/primitive types
export type Gender = "male" | "female" | "other" | "prefer_not_to_say";
export type UserType = "individual" | "family" | "student" | "other";
export type OrganizerType = "company" | "ngo";
export type DonorType = "user" | "company";
export type RecipientType = "ngo" | "cause";
export type DonationStatus = "pending" | "completed" | "failed" | "refunded";
export type CauseStatus = "draft" | "active" | "paused" | "completed" | "cancelled";

export interface Location {
  country: string;
  state?: string | null;
  city?: string | null;
}

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Address extends Location {
  street?: string | null;
  zipCode?: string | null;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  updates: boolean;
  campaigns: boolean;
}

export interface SocialMedia {
  facebook?: string | null;
  twitter?: string | null;
  instagram?: string | null;
  linkedin?: string | null;
}

// Collections
export interface UserDoc {
  // Basic
  uid: string;
  email: string;
  displayName?: string | null;
  photoURL?: string | null;
  phoneNumber?: string | null;
  // Profile
  firstName?: string | null;
  lastName?: string | null;
  dateOfBirth?: Timestamp | null;
  gender?: Gender | null;
  location?: Location | null;
  // App Specific
  userType?: UserType | string;
  interests?: string[];
  preferredLanguage?: string;
  // Donation summary
  totalDonated?: number;
  donationCount?: number;
  // Engagement
  favoriteNGOs?: string[]; // ngoIds
  followedCauses?: string[]; // causeIds
  // Settings
  notificationSettings?: NotificationSettings;
  // Metadata
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isActive: boolean;
  isVerified: boolean;
}

export interface NGODoc {
  // Basic Info
  id: {ngoId},
  name: "Save the Children",
  description: "Long description of NGO mission...",
  shortDescription: "Brief mission statement",
  foundedDate: timestamp,
  
  // Registration Info
  registrationNumber: "REG123456",
  isRegistered: true,
  registrationCountry: "Fi",
  
  // Contact Info
  email: "<EMAIL>",
  phone: "+1234567890",
  website: "https://ngo.org",
  
  // Address
  address: {
    street: "123 Main St",
    city: "Helsinki",
    region: "Uusimaa",
    country: "Finland",
    zipCode: "00180"
  },
  
  // Media
  registrationDoc: "https://...",
  logo: "https://...",
  coverImage: "https://...",
  images: ["https://...", "https://..."],
  
  // Categories & Focus
  primaryCategory: "education",
  subCategories: ["education", "children", "poverty"],
  
  // Verification & Trust
  status: ["to be reviewed", "approved", "rejected"]
  verificationDate: timestamp,
  
  // Social
  socialMedia: {
    facebook: "https://facebook.com/ngo",
    twitter: "https://twitter.com/ngo",
    instagram: "https://instagram.com/ngo",
    linkedin: "https://linkedin.com/company/ngo"
  },
  
  // Settings
  isActive: true,
  acceptsDonations: true,
  
  // Admin
  adminUsers: ["<EMAIL>", "<EMAIL>"],
  
  // Metadata
  createdAt: timestamp,
  updatedAt: timestamp,
  createdBy: "userId"
}

export interface CompanyDoc {
  name: string;
  description?: string | null;
  industry?: string | null;
  size?: "startup" | "small" | "medium" | "large" | "enterprise" | string;
  registrationNumber?: string | null;
  taxId?: string | null;
  legalStructure?: string | null;
  email?: string | null;
  phone?: string | null;
  website?: string | null;
  headquarters?: Address | null;
  logo?: string | null;
  coverImage?: string | null;
  csrFocus?: string[];
  annualCSRBudget?: number;
  employeeCount?: number;
  totalDonated?: number;
  campaignsSupported?: number;
  ngoPartnerships?: string[]; // ngoIds
  isVerified: boolean;
  verificationDate?: Timestamp | null;
  isActive: boolean;
  adminUsers: string[]; // emails
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CauseImpactMetric {
  metric: string;
  target: number;
  current: number;
}

export interface CauseDoc {
  title: string;
  description?: string | null;
  shortDescription?: string | null;
  createdBy: string; // ngoId | userId | companyId
  createdByType: "ngo" | "user" | "company";
  category: string;
  subcategory?: string | null;
  tags?: string[];
  goalAmount: number;
  raisedAmount: number;
  donorCount: number;
  startDate?: Timestamp | null;
  endDate?: Timestamp | null;
  duration?: number | null; // days
  featuredImage?: string | null;
  images?: string[];
  video?: string | null;
  location?: (Location & { coordinates?: Coordinates | null }) | null;
  expectedImpact?: string | null;
  impactMetrics?: CauseImpactMetric[];
  status: CauseStatus;
  isUrgent: boolean;
  isFeatured: boolean;
  lastUpdateDate?: Timestamp | null;
  updateCount?: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  views?: number;
  shares?: number;
}

export interface DonationDoc {
  amount: number;
  currency: string; // ISO 4217
  donorId: string; // userId or companyId
  donorType: DonorType;
  recipientId: string; // ngoId or causeId
  recipientType: RecipientType;
  causeId?: string | null;
  paymentMethod: string;
  paymentProvider: string; // e.g. stripe
  paymentId?: string | null; // provider payment intent id
  status: DonationStatus;
  isRecurring: boolean;
  recurringFrequency?: "monthly" | "quarterly" | "yearly" | null;
  recurringEndDate?: Timestamp | null;
  isAnonymous: boolean;
  message?: string | null;
  createdAt: Timestamp;
  processedAt?: Timestamp | null;
  taxReceiptRequested?: boolean;
  taxReceiptSent?: boolean;
  taxReceiptUrl?: string | null;
  impactUpdatesEnabled?: boolean;
}

export interface CampaignDoc {
  title: string;
  description?: string | null;
  organizerId: string; // companyId | ngoId
  organizerType: OrganizerType;
  participatingNGOs?: string[]; // ngoIds
  supportingCompanies?: string[]; // companyIds
  fundraisingGoal: number;
  totalRaised: number;
  participantGoal?: number;
  currentParticipants?: number;
  startDate?: Timestamp | null;
  endDate?: Timestamp | null;
  bannerImage?: string | null;
  images?: string[];
  minimumDonation?: number | null;
  maximumDonation?: number | null;
  allowAnonymous: boolean;
  status: "draft" | "active" | "completed" | "cancelled";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CategoryDoc {
  name: string;
  description?: string | null;
  icon?: string | null;
  color?: string | null;
  parentCategory?: string | null;
  subcategories?: string[];
  isActive: boolean;
  sortOrder?: number;
  createdAt: Timestamp;
}

export interface UpdateImpactMetric {
  metric: string;
  previous: number;
  current: number;
  change: number;
}

export interface UpdateDoc {
  entityId: string; // causeId | ngoId | campaignId
  entityType: "cause" | "ngo" | "campaign";
  title: string;
  content: string;
  images?: string[];
  video?: string | null;
  authorId: string; // ngoId | companyId | userId
  authorType: "ngo" | "company" | "user";
  authorName?: string | null;
  impactMetrics?: UpdateImpactMetric[];
  likes?: number;
  comments?: number;
  shares?: number;
  createdAt: Timestamp;
  isPublic: boolean;
}

// Subcollections (optional interfaces)
export interface CauseCommentDoc {
  authorId: string; // userId
  content: string;
  createdAt: Timestamp;
}

export interface NgoTeamMemberDoc {
  userId: string;
  role: string;
  joinedAt: Timestamp;
}

export interface ReportDoc {
  title: string;
  url: string;
  publishedAt: Timestamp;
}




