import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore";
import { db } from "./firebase";
import { User } from "firebase/auth";

export interface UserData {
  id: string;
  userId: string;
  email: string;
  name: string;
  surname: string;
  photoUrl?: string | null;
  ngoId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export class UserService {
  static async getUserData(userId: string): Promise<UserData | null> {
    try {
      const userDoc = doc(db, "users", userId);
      const userSnapshot = await getDoc(userDoc);
      
      if (userSnapshot.exists()) {
        const data = userSnapshot.data();
        return {
          id: userSnapshot.id,
          userId: data.userId || userSnapshot.id,
          email: data.email,
          name: data.name || '',
          surname: data.surname || '',
          photoUrl: data.photoUrl || null,
          ngoId: data.ngoId || null,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        };
      }
      
      return null;
    } catch (error) {
      console.error("Error fetching user data:", error);
      throw error;
    }
  }

  static async createUserData(user: User, userInfo?: { name: string; surname: string; photoUrl?: string }): Promise<UserData> {
    try {
      const userData: Omit<UserData, 'id'> = {
        userId: user.uid,
        email: user.email || '',
        name: userInfo?.name || '',
        surname: userInfo?.surname || '',
        photoUrl: userInfo?.photoUrl || null,
        ngoId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const userDoc = doc(db, "users", user.uid);
      await setDoc(userDoc, userData);

      return {
        id: user.uid,
        ...userData,
      };
    } catch (error) {
      console.error("Error creating user data:", error);
      throw error;
    }
  }

  static async updateUserNgoId(userId: string, ngoId: string): Promise<void> {
    try {
      const userDoc = doc(db, "users", userId);
      await updateDoc(userDoc, {
        ngoId: ngoId,
        updatedAt: new Date(),
      });
    } catch (error) {
      console.error("Error updating user NGO ID:", error);
      throw error;
    }
  }

  static async checkUserHasNgo(userId: string): Promise<boolean> {
    try {
      const userData = await this.getUserData(userId);
      return userData ? !!(userData.ngoId && userData.ngoId.trim() !== '') : false;
    } catch (error) {
      console.error("Error checking user NGO status:", error);
      return false;
    }
  }
} 