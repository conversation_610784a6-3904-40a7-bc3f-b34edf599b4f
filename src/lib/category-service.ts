import { collection, getDocs, query, where } from "firebase/firestore";
import { db } from "./firebase";
import type { CategoryDoc } from "./types";

export interface Category extends CategoryDoc {
  id: string;
}

export class CategoryService {
  static async getAllCategories(): Promise<Category[]> {
    try {
      const ref = collection(db, "categories");
      const snap = await getDocs(ref);
      const categories: Category[] = snap.docs.map((d) => ({ id: d.id, ...(d.data() as CategoryDoc) }));
      return CategoryService.sortCategories(categories);
    } catch (error) {
      console.error("Error fetching categories:", error);
      throw new Error("Failed to fetch categories. Please try again.");
    }
  }

  static async getActiveCategories(): Promise<Category[]> {
    try {
      const ref = query(collection(db, "categories"), where("isActive", "==", true));
      const snap = await getDocs(ref);
      const categories: Category[] = snap.docs.map((d) => ({ id: d.id, ...(d.data() as CategoryDoc) }));
      return CategoryService.sortCategories(categories);
    } catch (error) {
      console.error("Error fetching active categories:", error);
      throw new Error("Failed to fetch active categories. Please try again.");
    }
  }

  private static sortCategories(categories: Category[]): Category[] {
    return [...categories].sort((a, b) => {
      const aOrder = a.sortOrder ?? Number.MAX_SAFE_INTEGER;
      const bOrder = b.sortOrder ?? Number.MAX_SAFE_INTEGER;
      if (aOrder !== bOrder) return aOrder - bOrder;
      return a.name.localeCompare(b.name);
    });
  }
}


