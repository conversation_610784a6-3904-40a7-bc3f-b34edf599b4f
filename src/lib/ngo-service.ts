import { doc, serverTimestamp, setDoc, updateDoc } from "firebase/firestore";
import { getDownloadURL, ref, uploadBytesResumable } from "firebase/storage";
import { db, storage } from "./firebase";

export type UploadedDocRef = {
  name: string;
  url: string;
  type: string;
  size: number;
  uploadedAt: Date;
};

export class NgoService {
  static async createNgoPhase1(ngoId: string, data: Record<string, unknown> & { registrationDocs?: UploadedDocRef[]; createdBy: string }) {
    const refDoc = doc(db, "ngos", ngoId);
    await setDoc(refDoc, {
      ...data,
      status: "to be reviewed",
      isVerified: false,
      acceptsDonations: false,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    }, { merge: true });
  }

  static async appendRegistrationDocs(ngoId: string, docs: UploadedDocRef[]) {
    const refDoc = doc(db, "ngos", ngoId);
    await updateDoc(refDoc, {
      registrationDocs: docs,
      updatedAt: serverTimestamp(),
    });
  }

  static async uploadRegistrationDocument(params: {
    ngoId: string;
    file: File;
    onProgress?: (pct: number) => void;
  }): Promise<UploadedDocRef> {
    const { ngoId, file, onProgress } = params;
    const MAX_BYTES = 10 * 1024 * 1024; // 10MB
    const allowed = new Set([
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "image/jpeg",
      "image/png",
    ]);
    if (!allowed.has(file.type)) {
      throw new Error("Unsupported file type. Allowed: PDF, DOC, DOCX, JPG, PNG.");
    }
    if (file.size > MAX_BYTES) {
      throw new Error("File too large. Max size is 10MB.");
    }

    const safeName = file.name.replace(/[^a-zA-Z0-9._-]/g, "_");
    const unique = `${Date.now()}_${Math.random().toString(36).slice(2, 8)}_${safeName}`;
    const path = `ngos/${ngoId}/registrationDocs/${unique}`;
    const storageRef = ref(storage, path);
    const task = uploadBytesResumable(storageRef, file, { contentType: file.type });
    await new Promise<void>((resolve, reject) => {
      task.on("state_changed", (snap) => {
        if (onProgress) onProgress(Math.round((snap.bytesTransferred / snap.totalBytes) * 100));
      }, reject, () => resolve());
    });
    const url = await getDownloadURL(task.snapshot.ref);
    return { name: file.name, url, type: file.type, size: file.size, uploadedAt: new Date() };
  }
}


