import { useState } from "react";
import { Plus, Edit, Trash2, Target, Users, Calendar, Heart } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";

const causesData = [
  {
    id: 1,
    title: "Clean Water Initiative",
    description: "Providing clean drinking water access to rural communities in our region.",
    goal: 50000,
    raised: 32000,
    status: "active",
    deadline: "April 30, 2024",
    volunteers: 45,
    category: "Environment"
  },
  {
    id: 2,
    title: "Education for All",
    description: "Building classrooms and providing educational materials for underprivileged children.",
    goal: 25000,
    raised: 18500,
    status: "active",
    deadline: "May 15, 2024",
    volunteers: 28,
    category: "Education"
  },
  {
    id: 3,
    title: "Medical Aid Fund",
    description: "Emergency medical assistance for families in need of critical healthcare.",
    goal: 75000,
    raised: 45000,
    status: "active",
    deadline: "June 1, 2024",
    volunteers: 62,
    category: "Healthcare"
  },
  {
    id: 4,
    title: "Food Security Program",
    description: "Ensuring nutritious meals for homeless individuals and families.",
    goal: 30000,
    raised: 30000,
    status: "completed",
    deadline: "March 1, 2024",
    volunteers: 38,
    category: "Food Aid"
  }
];

export default function Causes() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const filteredCauses = causesData.filter(cause => {
    const matchesSearch = cause.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cause.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || cause.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-success text-success-foreground";
      case "completed": return "bg-primary text-primary-foreground";
      case "paused": return "bg-warning text-warning-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Causes</h1>
          <p className="text-muted-foreground">Manage your NGO's campaigns and fundraising initiatives.</p>
        </div>
        <Button className="bg-gradient-to-r from-primary to-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Create New Cause
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Input
          placeholder="Search causes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-xs"
        />
        <div className="flex gap-2">
          <Button
            variant={statusFilter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("all")}
          >
            All
          </Button>
          <Button
            variant={statusFilter === "active" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("active")}
          >
            Active
          </Button>
          <Button
            variant={statusFilter === "completed" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("completed")}
          >
            Completed
          </Button>
        </div>
      </div>

      {/* Causes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCauses.map((cause) => {
          const progressPercentage = (cause.raised / cause.goal) * 100;
          
          return (
            <Card key={cause.id} className="hover:shadow-medium transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{cause.title}</CardTitle>
                    <CardDescription className="mt-2">
                      {cause.description}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(cause.status)}>
                    {cause.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className="font-medium">${cause.raised.toLocaleString()} / ${cause.goal.toLocaleString()}</span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />
                  <div className="text-right text-xs text-muted-foreground">
                    {progressPercentage.toFixed(1)}% completed
                  </div>
                </div>

                {/* Stats */}
                <div className="flex justify-between text-sm">
                  <div className="flex items-center text-muted-foreground">
                    <Users className="w-4 h-4 mr-1" />
                    {cause.volunteers} volunteers
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <Calendar className="w-4 h-4 mr-1" />
                    {cause.deadline}
                  </div>
                </div>

                <Badge variant="secondary" className="w-fit">
                  {cause.category}
                </Badge>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button size="sm" className="flex-1">
                    <Target className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                  <Button variant="outline" size="sm">
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredCauses.length === 0 && (
        <div className="text-center py-12">
          <Heart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No causes found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || statusFilter !== "all" 
              ? "Try adjusting your search or filters" 
              : "Create your first cause to start making an impact"}
          </p>
          <Button className="bg-gradient-to-r from-primary to-primary/90">
            <Plus className="w-4 h-4 mr-2" />
            Create New Cause
          </Button>
        </div>
      )}
    </div>
  );
}