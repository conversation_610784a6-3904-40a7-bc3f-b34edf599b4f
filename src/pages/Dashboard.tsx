import { Plus, TrendingUp, Heart, Calendar, DollarSign, Users } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const stats = [
  {
    title: "Total Donations",
    value: "$45,231",
    change: "+12%",
    icon: DollarSign,
    gradient: "from-primary to-primary/80"
  },
  {
    title: "Active Causes",
    value: "8",
    change: "+2",
    icon: Heart,
    gradient: "from-accent to-accent/80"
  },
  {
    title: "Upcoming Activities",
    value: "12",
    change: "+3",
    icon: Calendar,
    gradient: "from-warning to-warning/80"
  },
  {
    title: "Volunteers",
    value: "156",
    change: "+24",
    icon: Users,
    gradient: "from-success to-success/80"
  }
];

const recentDonations = [
  { donor: "Sarah Johnson", amount: "$500", cause: "Clean Water Initiative", time: "2 hours ago" },
  { donor: "Tech Corp Ltd", amount: "$2,000", cause: "Education for All", time: "4 hours ago" },
  { donor: "Anonymous", amount: "$250", cause: "Medical Aid Fund", time: "6 hours ago" },
  { donor: "Michael Chen", amount: "$100", cause: "Food Security", time: "8 hours ago" },
];

const upcomingActivities = [
  { title: "Community Health Camp", date: "March 15, 2024", volunteers: 25, location: "Downtown Center" },
  { title: "Tree Plantation Drive", date: "March 18, 2024", volunteers: 40, location: "City Park" },
  { title: "Educational Workshop", date: "March 22, 2024", volunteers: 15, location: "School Premises" },
];

export default function Dashboard() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's what's happening with your NGO.</p>
        </div>
        <div className="flex space-x-3">
          <Button className="bg-gradient-to-r from-primary to-primary/90">
            <Plus className="w-4 h-4 mr-2" />
            Create Cause
          </Button>
          <Button variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            Plan Activity
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title} className="relative overflow-hidden">
            <div className={`absolute inset-0 bg-gradient-to-br ${stat.gradient} opacity-5`} />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="w-3 h-3 mr-1 text-success" />
                <span className="text-success">{stat.change}</span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Donations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Recent Donations
              <Button variant="outline" size="sm">View All</Button>
            </CardTitle>
            <CardDescription>
              Latest contributions to your causes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentDonations.map((donation, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div className="flex-1">
                    <div className="font-medium text-foreground">{donation.donor}</div>
                    <div className="text-sm text-muted-foreground">{donation.cause}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-primary">{donation.amount}</div>
                    <div className="text-xs text-muted-foreground">{donation.time}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Upcoming Activities
              <Button variant="outline" size="sm">View All</Button>
            </CardTitle>
            <CardDescription>
              Your scheduled events and activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingActivities.map((activity, index) => (
                <div key={index} className="p-3 rounded-lg border border-border">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-foreground">{activity.title}</div>
                      <div className="text-sm text-muted-foreground flex items-center mt-1">
                        <Calendar className="w-3 h-3 mr-1" />
                        {activity.date}
                      </div>
                      <div className="text-sm text-muted-foreground">{activity.location}</div>
                    </div>
                    <Badge variant="secondary" className="ml-2">
                      {activity.volunteers} volunteers
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}