import { useState } from "react";
import { Building2, Upload, Save, Edit, Globe, Phone, Mail, MapPin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

export default function Profile() {
  const [isEditing, setIsEditing] = useState(false);
  const [ngoData, setNgoData] = useState({
    name: "Hope Foundation",
    description: "Dedicated to improving lives through education, healthcare, and community development programs.",
    mission: "To create sustainable positive change in underserved communities by providing access to quality education, healthcare services, and economic opportunities.",
    category: "Multi-purpose",
    email: "<EMAIL>",
    phone: "+****************",
    address: "123 Main Street, City, State 12345",
    website: "www.hopefoundation.org",
    facebook: "facebook.com/hopefoundation",
    twitter: "twitter.com/hopefoundation",
    instagram: "instagram.com/hopefoundation",
    taxId: "12-3456789",
    founded: "2018",
    registrationNumber: "NGO-2018-001"
  });
  const { toast } = useToast();

  const handleSave = () => {
    setIsEditing(false);
    toast({
      title: "Profile updated",
      description: "Your NGO profile has been successfully updated.",
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setNgoData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">NGO Profile</h1>
          <p className="text-muted-foreground">Manage your organization's information and settings.</p>
        </div>
        <div className="flex gap-3">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button className="bg-gradient-to-r from-primary to-primary/90" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Logo and Basic Info */}
        <Card>
          <CardHeader>
            <CardTitle>Organization Logo</CardTitle>
            <CardDescription>Upload your NGO's official logo</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-32 h-32 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center">
                <Building2 className="w-16 h-16 text-white" />
              </div>
              {isEditing && (
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Logo
                </Button>
              )}
            </div>
            
            <div className="space-y-3 pt-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Founded</Label>
                <p className="font-medium">{ngoData.founded}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Category</Label>
                <Badge variant="secondary" className="mt-1">
                  {ngoData.category}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Registration Number</Label>
                <p className="text-sm">{ngoData.registrationNumber}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Core details about your organization</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Organization Name</Label>
                  {isEditing ? (
                    <Input
                      id="name"
                      value={ngoData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                    />
                  ) : (
                    <p className="font-medium">{ngoData.name}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  {isEditing ? (
                    <Input
                      id="category"
                      value={ngoData.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                    />
                  ) : (
                    <p className="font-medium">{ngoData.category}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                {isEditing ? (
                  <Textarea
                    id="description"
                    value={ngoData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                  />
                ) : (
                  <p>{ngoData.description}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="mission">Mission Statement</Label>
                {isEditing ? (
                  <Textarea
                    id="mission"
                    value={ngoData.mission}
                    onChange={(e) => handleInputChange('mission', e.target.value)}
                    rows={4}
                  />
                ) : (
                  <p>{ngoData.mission}</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>How people can reach your organization</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center">
                    <Mail className="w-4 h-4 mr-2" />
                    Email
                  </Label>
                  {isEditing ? (
                    <Input
                      id="email"
                      type="email"
                      value={ngoData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                    />
                  ) : (
                    <p>{ngoData.email}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center">
                    <Phone className="w-4 h-4 mr-2" />
                    Phone
                  </Label>
                  {isEditing ? (
                    <Input
                      id="phone"
                      value={ngoData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                    />
                  ) : (
                    <p>{ngoData.phone}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address" className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2" />
                  Address
                </Label>
                {isEditing ? (
                  <Textarea
                    id="address"
                    value={ngoData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    rows={2}
                  />
                ) : (
                  <p>{ngoData.address}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="website" className="flex items-center">
                  <Globe className="w-4 h-4 mr-2" />
                  Website
                </Label>
                {isEditing ? (
                  <Input
                    id="website"
                    value={ngoData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                  />
                ) : (
                  <p>{ngoData.website}</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Social Media</CardTitle>
              <CardDescription>Connect your social media profiles</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="facebook">Facebook</Label>
                  {isEditing ? (
                    <Input
                      id="facebook"
                      value={ngoData.facebook}
                      onChange={(e) => handleInputChange('facebook', e.target.value)}
                    />
                  ) : (
                    <p className="text-sm">{ngoData.facebook}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="twitter">Twitter</Label>
                  {isEditing ? (
                    <Input
                      id="twitter"
                      value={ngoData.twitter}
                      onChange={(e) => handleInputChange('twitter', e.target.value)}
                    />
                  ) : (
                    <p className="text-sm">{ngoData.twitter}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="instagram">Instagram</Label>
                  {isEditing ? (
                    <Input
                      id="instagram"
                      value={ngoData.instagram}
                      onChange={(e) => handleInputChange('instagram', e.target.value)}
                    />
                  ) : (
                    <p className="text-sm">{ngoData.instagram}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Legal Information</CardTitle>
              <CardDescription>Official registration and tax details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="taxId">Tax ID</Label>
                  {isEditing ? (
                    <Input
                      id="taxId"
                      value={ngoData.taxId}
                      onChange={(e) => handleInputChange('taxId', e.target.value)}
                    />
                  ) : (
                    <p>{ngoData.taxId}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="regNumber">Registration Number</Label>
                  {isEditing ? (
                    <Input
                      id="regNumber"
                      value={ngoData.registrationNumber}
                      onChange={(e) => handleInputChange('registrationNumber', e.target.value)}
                    />
                  ) : (
                    <p>{ngoData.registrationNumber}</p>
                  )}
                </div>
              </div>

              {isEditing && (
                <div className="space-y-2">
                  <Label>Legal Documents</Label>
                  <Button variant="outline" className="w-full">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Registration Certificate
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}