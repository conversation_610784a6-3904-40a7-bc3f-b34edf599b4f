import { useState } from "react";
import { Plus, Edit, Trash2, MapPin, Users, Calendar, Clock } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";

const activitiesData = [
  {
    id: 1,
    title: "Community Health Camp",
    description: "Free health checkups and medical consultations for local residents.",
    date: "March 15, 2024",
    time: "9:00 AM - 5:00 PM",
    location: "Downtown Community Center",
    volunteers: 25,
    requiredVolunteers: 30,
    status: "upcoming",
    category: "Healthcare"
  },
  {
    id: 2,
    title: "Tree Plantation Drive",
    description: "Environmental initiative to plant 500 trees in the city park area.",
    date: "March 18, 2024",
    time: "7:00 AM - 12:00 PM",
    location: "Central City Park",
    volunteers: 40,
    requiredVolunteers: 40,
    status: "confirmed",
    category: "Environment"
  },
  {
    id: 3,
    title: "Educational Workshop",
    description: "Teaching basic computer skills to underprivileged students.",
    date: "March 22, 2024",
    time: "2:00 PM - 6:00 PM",
    location: "Local School Premises",
    volunteers: 15,
    requiredVolunteers: 20,
    status: "upcoming",
    category: "Education"
  },
  {
    id: 4,
    title: "Food Distribution",
    description: "Distributing meals to homeless individuals and families in need.",
    date: "March 10, 2024",
    time: "6:00 PM - 9:00 PM",
    location: "City Shelter",
    volunteers: 35,
    requiredVolunteers: 30,
    status: "completed",
    category: "Food Aid"
  }
];

export default function Activities() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const filteredActivities = activitiesData.filter(activity => {
    const matchesSearch = activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || activity.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "upcoming": return "bg-warning text-warning-foreground";
      case "confirmed": return "bg-success text-success-foreground";
      case "completed": return "bg-primary text-primary-foreground";
      case "cancelled": return "bg-destructive text-destructive-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getVolunteerStatus = (current: number, required: number) => {
    if (current >= required) return { text: "Full", color: "bg-success text-success-foreground" };
    if (current >= required * 0.8) return { text: "Almost Full", color: "bg-warning text-warning-foreground" };
    return { text: "Need More", color: "bg-destructive text-destructive-foreground" };
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Activities</h1>
          <p className="text-muted-foreground">Plan and manage your NGO's events and volunteer activities.</p>
        </div>
        <Button className="bg-gradient-to-r from-primary to-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Plan New Activity
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Input
          placeholder="Search activities..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-xs"
        />
        <div className="flex gap-2">
          <Button
            variant={statusFilter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("all")}
          >
            All
          </Button>
          <Button
            variant={statusFilter === "upcoming" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("upcoming")}
          >
            Upcoming
          </Button>
          <Button
            variant={statusFilter === "confirmed" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("confirmed")}
          >
            Confirmed
          </Button>
          <Button
            variant={statusFilter === "completed" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("completed")}
          >
            Completed
          </Button>
        </div>
      </div>

      {/* Activities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredActivities.map((activity) => {
          const volunteerStatus = getVolunteerStatus(activity.volunteers, activity.requiredVolunteers);
          
          return (
            <Card key={activity.id} className="hover:shadow-medium transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{activity.title}</CardTitle>
                    <CardDescription className="mt-2">
                      {activity.description}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(activity.status)}>
                    {activity.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Date & Time */}
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="w-4 h-4 mr-2" />
                    {activity.date}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="w-4 h-4 mr-2" />
                    {activity.time}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="w-4 h-4 mr-2" />
                    {activity.location}
                  </div>
                </div>

                {/* Volunteer Status */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Volunteers</span>
                    <Badge variant="secondary" className={volunteerStatus.color}>
                      {volunteerStatus.text}
                    </Badge>
                  </div>
                  <div className="flex items-center text-sm">
                    <Users className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span className="font-medium">{activity.volunteers}</span>
                    <span className="text-muted-foreground mx-1">/</span>
                    <span className="text-muted-foreground">{activity.requiredVolunteers} needed</span>
                  </div>
                </div>

                <Badge variant="secondary" className="w-fit">
                  {activity.category}
                </Badge>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button size="sm" className="flex-1">
                    View Details
                  </Button>
                  <Button variant="outline" size="sm">
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredActivities.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No activities found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || statusFilter !== "all" 
              ? "Try adjusting your search or filters" 
              : "Plan your first activity to engage volunteers"}
          </p>
          <Button className="bg-gradient-to-r from-primary to-primary/90">
            <Plus className="w-4 h-4 mr-2" />
            Plan New Activity
          </Button>
        </div>
      )}
    </div>
  );
}