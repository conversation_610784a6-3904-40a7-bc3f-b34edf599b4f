import { useState } from "react";
import { Download, Filter, Search, DollarSign, TrendingUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";

const donationsData = [
  {
    id: 1,
    donor: "<PERSON>",
    email: "<EMAIL>",
    amount: 500,
    cause: "Clean Water Initiative",
    type: "individual",
    date: "2024-03-12",
    time: "14:30",
    status: "completed"
  },
  {
    id: 2,
    donor: "Tech Corp Ltd",
    email: "<EMAIL>",
    amount: 2000,
    cause: "Education for All",
    type: "corporate",
    date: "2024-03-12",
    time: "10:15",
    status: "completed"
  },
  {
    id: 3,
    donor: "Anonymous",
    email: "",
    amount: 250,
    cause: "Medical Aid Fund",
    type: "individual",
    date: "2024-03-11",
    time: "16:45",
    status: "completed"
  },
  {
    id: 4,
    donor: "<PERSON>",
    email: "<EMAIL>",
    amount: 100,
    cause: "Food Security Program",
    type: "individual",
    date: "2024-03-11",
    time: "09:20",
    status: "completed"
  },
  {
    id: 5,
    donor: "Green Solutions Inc",
    email: "<EMAIL>",
    amount: 1500,
    cause: "Tree Plantation Drive",
    type: "corporate",
    date: "2024-03-10",
    time: "13:10",
    status: "pending"
  },
  {
    id: 6,
    donor: "Maria Rodriguez",
    email: "<EMAIL>",
    amount: 75,
    cause: "Educational Workshop",
    type: "individual",
    date: "2024-03-10",
    time: "11:30",
    status: "completed"
  }
];

const summaryStats = [
  {
    title: "Total Donations",
    value: "$45,231",
    change: "+12.5%",
    period: "this month"
  },
  {
    title: "Average Donation",
    value: "$287",
    change: "+8.2%",
    period: "this month"
  },
  {
    title: "Total Donors",
    value: "284",
    change: "+15.3%",
    period: "this month"
  },
  {
    title: "Corporate Donors",
    value: "23",
    change: "+4.2%",
    period: "this month"
  }
];

export default function Donations() {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  const filteredDonations = donationsData.filter(donation => {
    const matchesSearch = donation.donor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         donation.cause.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === "all" || donation.type === typeFilter;
    const matchesStatus = statusFilter === "all" || donation.status === statusFilter;
    return matchesSearch && matchesType && matchesStatus;
  });

  const getTypeColor = (type: string) => {
    switch (type) {
      case "corporate": return "bg-primary text-primary-foreground";
      case "individual": return "bg-accent text-accent-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-success text-success-foreground";
      case "pending": return "bg-warning text-warning-foreground";
      case "failed": return "bg-destructive text-destructive-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Donations</h1>
          <p className="text-muted-foreground">Track and manage all donations to your NGO.</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
          <Button className="bg-gradient-to-r from-primary to-primary/90">
            <Download className="w-4 h-4 mr-2" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {summaryStats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="w-3 h-3 mr-1 text-success" />
                <span className="text-success">{stat.change}</span>
                <span className="ml-1">{stat.period}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
          <Input
            placeholder="Search donations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant={typeFilter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setTypeFilter("all")}
          >
            All Types
          </Button>
          <Button
            variant={typeFilter === "individual" ? "default" : "outline"}
            size="sm"
            onClick={() => setTypeFilter("individual")}
          >
            Individual
          </Button>
          <Button
            variant={typeFilter === "corporate" ? "default" : "outline"}
            size="sm"
            onClick={() => setTypeFilter("corporate")}
          >
            Corporate
          </Button>
        </div>
        <div className="flex gap-2">
          <Button
            variant={statusFilter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("all")}
          >
            All Status
          </Button>
          <Button
            variant={statusFilter === "completed" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("completed")}
          >
            Completed
          </Button>
          <Button
            variant={statusFilter === "pending" ? "default" : "outline"}
            size="sm"
            onClick={() => setStatusFilter("pending")}
          >
            Pending
          </Button>
        </div>
      </div>

      {/* Donations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Donations</CardTitle>
          <CardDescription>
            A detailed view of all donations received
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Donor</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Cause</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDonations.map((donation) => (
                <TableRow key={donation.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{donation.donor}</div>
                      {donation.email && (
                        <div className="text-sm text-muted-foreground">{donation.email}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="font-semibold text-primary">
                    ${donation.amount.toLocaleString()}
                  </TableCell>
                  <TableCell>{donation.cause}</TableCell>
                  <TableCell>
                    <Badge className={getTypeColor(donation.type)}>
                      {donation.type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>{donation.date}</div>
                      <div className="text-sm text-muted-foreground">{donation.time}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(donation.status)}>
                      {donation.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {filteredDonations.length === 0 && (
        <div className="text-center py-12">
          <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No donations found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search or filters to see more results
          </p>
        </div>
      )}
    </div>
  );
}