import { useMemo, useState } from "react";
import { Upload, X, Link as LinkIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { useCategoriesActive } from "@/hooks/use-firestore-queries";
import { NgoService } from "@/lib/ngo-service";

export default function CreateNGO() {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    primaryCategory: "",

    isRegistered: false,
    registrationNumber: "",
    taxId: "",
    registrationCountry: "",
    legalStatus: "",
    registrationDoc: "",

    email: "",
    phone: "",
    website: "",

    address: {
      country: "",
      city: "",
    },
  });
  const [isLoading, setIsLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { logout } = useAuthContext();

  // Legal status options removed from UI per requirement

  const countryOptions = useMemo(
    () => [
      { code: "FI", name: "Finland" },
      { code: "SE", name: "Sweden" },
      { code: "NO", name: "Norway" },
      { code: "DK", name: "Denmark" },
      { code: "IS", name: "Iceland" },
      { code: "EE", name: "Estonia" },
      { code: "LV", name: "Latvia" },
      { code: "LT", name: "Lithuania" },
      { code: "DE", name: "Germany" },
      { code: "FR", name: "France" },
      { code: "ES", name: "Spain" },
      { code: "IT", name: "Italy" },
      { code: "NL", name: "Netherlands" },
      { code: "BE", name: "Belgium" },
      { code: "IE", name: "Ireland" },
      { code: "GB", name: "United Kingdom" },
      { code: "US", name: "United States" },
      { code: "CA", name: "Canada" },
      { code: "BR", name: "Brazil" },
      { code: "AR", name: "Argentina" },
      { code: "MX", name: "Mexico" },
      { code: "IN", name: "India" },
      { code: "CN", name: "China" },
      { code: "JP", name: "Japan" },
      { code: "KR", name: "South Korea" },
      { code: "SG", name: "Singapore" },
      { code: "AU", name: "Australia" },
      { code: "NZ", name: "New Zealand" },
      { code: "KE", name: "Kenya" },
      { code: "ZA", name: "South Africa" },
    ],
    []
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const error = validateForm();
    if (error) {
      toast({ title: "Validation error", description: error, variant: "destructive" });
      return;
    }

    setIsLoading(true);
    setTimeout(() => {
      localStorage.setItem("ngoExists", "true");
      localStorage.setItem("ngoData", JSON.stringify(formData));
      toast({ title: "Submitted for review", description: "Your NGO is now pending admin review." });
      navigate("/dashboard");
      setIsLoading(false);
    }, 800);
  };

  const handleCancel = async () => {
    try {
      const result = await logout();
      if (result.success) {
        toast({
          title: "Logged out successfully",
          description: "You have been signed out",
        });
        navigate("/login");
      } else {
        toast({
          title: "Logout failed",
          description: result.error || "Failed to logout",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const { data: categories, isLoading: isCategoriesLoading, isError: isCategoriesError } = useCategoriesActive();

  function isValidEmail(value: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
  }

  function isValidUrl(value: string): boolean {
    if (!value) return true;
    try {
      const u = new URL(value);
      return Boolean(u.protocol === "http:" || u.protocol === "https:");
    } catch (_) {
      return false;
    }
  }

  function validateForm(): string | null {
    if (!formData.name.trim()) return "Organization name is required.";
    if (!formData.description.trim()) return "Organization description is required.";
    if (!formData.primaryCategory) return "Primary category is required.";
    if (!formData.email || !isValidEmail(formData.email)) return "A valid contact email is required.";
    if (!formData.address.country) return "Country is required.";

    if (formData.isRegistered) {
      const hasRegId = Boolean(formData.registrationNumber?.trim()) || Boolean(formData.taxId?.trim());
      if (!hasRegId) return "Provide a registration number or tax ID.";
      // if (!formData.registrationCountry) return "Registration country is required for registered NGOs.";
    }

    if (formData.website && !isValidUrl(formData.website)) return "Website URL is invalid.";
    if (formData.registrationDoc && !isValidUrl(formData.registrationDoc)) return "Registration document URL is invalid.";
    return null;
  }

  async function handleFileSelected(file: File) {
    try {
      setUploading(true);
      setUploadProgress(0);
      // For phase 1 before NGO doc exists, we can stage uploads under a temp id (e.g., userId)
      // or after creating NGO, use real ngoId. Here we fallback to user uid as container.
      const ngoId = "temp"; // Replace with actual ngoId post-create
      const uploaded = await NgoService.uploadRegistrationDocument({
        ngoId,
        file,
        onProgress: (pct) => setUploadProgress(pct),
      });
      setFormData((prev) => ({ ...prev, registrationDoc: uploaded.url }));
      toast({ title: "Uploaded", description: "Registration document uploaded successfully." });
    } catch (error: any) {
      toast({ title: "Upload failed", description: error?.message || "Unable to upload document.", variant: "destructive" });
    } finally {
      setUploading(false);
      setUploadProgress(null);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-muted to-accent-muted flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl shadow-strong relative">
        {/* X Button */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 h-8 w-8 p-0 rounded-full hover:bg-muted"
          onClick={handleCancel}
        >
          <X className="h-4 w-4" />
        </Button>
        
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 rounded-2xl flex items-center justify-center mb-4">
            {/* <Heart className="w-8 h-8 text-white" /> */}
            <img src="/daily-angel-logo-white.png" alt="NGO Logo" className="w-auto h-auto" />
          </div>
          <CardTitle className="text-2xl font-bold">Create Your NGO Profile</CardTitle>
          <CardDescription>
            Set up your organization to start managing causes and activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">NGO Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter your NGO name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="primaryCategory">Primary Category *</Label>
                <Select
                  value={formData.primaryCategory}
                  onValueChange={(value) => setFormData({ ...formData, primaryCategory: value })}
                  disabled={isCategoriesLoading || isCategoriesError || !categories?.length}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        isCategoriesLoading
                          ? "Loading categories..."
                          : isCategoriesError
                          ? "Failed to load categories"
                          : "Select category"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {categories?.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {isCategoriesError && (
                  <p className="text-sm text-destructive">Unable to load categories. Please refresh the page.</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Mission & Description *</Label>
              <Textarea
                id="description"
                placeholder="Describe your NGO's mission and goals..."
                className="min-h-[100px]"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                required
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between rounded-md border p-4">
                <div
                  className="space-y-1 cursor-pointer select-none"
                  onClick={() => setFormData((prev) => ({ ...prev, isRegistered: !prev.isRegistered }))}
                >
                  <Label>Registered NGO *</Label>
                  <p className="text-xs text-muted-foreground">Set this if your organization is legally registered.</p>
                </div>
                <Switch
                  checked={formData.isRegistered}
                  onCheckedChange={(checked) => setFormData({ ...formData, isRegistered: checked })}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="registrationNumber">Y-tunnus (if registered)</Label>
                  <Input
                    id="registrationNumber"
                    placeholder="Official registration number"
                    value={formData.registrationNumber}
                    onChange={(e) => setFormData({ ...formData, registrationNumber: e.target.value })}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Contact Email *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone (optional)</Label>
                <Input
                  id="phone"
                  placeholder="+358..."
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="website">Website (optional)</Label>
                <Input
                  id="website"
                  placeholder="https://ngo.org"
                  value={formData.website}
                  onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                  type="url"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="addressCountry">Country *</Label>
                <Select
                  value={formData.address.country}
                  onValueChange={(value) => setFormData({ ...formData, address: { ...formData.address, country: value } })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countryOptions.map((c) => (
                      <SelectItem key={c.code} value={c.code}>
                        {c.name} ({c.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="addressCity">City (optional)</Label>
                <Input
                  id="addressCity"
                  placeholder="City"
                  value={formData.address.city}
                  onChange={(e) => setFormData({ ...formData, address: { ...formData.address, city: e.target.value } })}
                />
              </div>
            </div>

            <div className="space-y-3">
                <Label>Registration Document (PDF/Image or URL) (if registered)</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Upload PDF or image as proof of registration</p>
                  <div className="mt-3 flex flex-col items-center justify-center gap-3">
                    <input
                      id="fileUpload"
                      type="file"
                      accept="application/pdf,image/png,image/jpeg,image/webp"
                      onChange={(e) => {
                        const f = e.target.files?.[0];
                        if (f) void handleFileSelected(f);
                      }}
                      className="hidden"
                    />
                    <Button type="button" variant="outline" onClick={() => document.getElementById("fileUpload")?.click()} disabled={uploading}>
                      {uploading ? `Uploading${uploadProgress !== null ? ` ${uploadProgress}%` : ""}...` : "Choose File"}
                    </Button>
                    <div className="flex items-center gap-2 w-full max-w-md">
                      <LinkIcon className="h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="or paste a public URL (https://...)"
                        value={formData.registrationDoc}
                        onChange={(e) => setFormData({ ...formData, registrationDoc: e.target.value })}
                      />
                    </div>
                  </div>
                </div>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-gradient-to-r from-primary to-accent hover:opacity-90 transition-opacity"
              disabled={isLoading}
            >
              {isLoading ? "Submitting..." : "Submit for Admin Review"}
            </Button>
          </form>
          
          {/* Cancel Button */}
          <div className="mt-6 text-center">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleCancel}
              className="w-full"
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}