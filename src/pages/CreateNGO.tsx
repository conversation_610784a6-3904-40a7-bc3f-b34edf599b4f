import { useState } from "react";
import { Heart, Upload, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useAuthContext } from "@/components/auth/AuthProvider";

export default function CreateNGO() {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    contact: "",
    taxId: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { logout } = useAuthContext();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate NGO creation
    setTimeout(() => {
      if (formData.name && formData.description && formData.category) {
        localStorage.setItem("ngoExists", "true");
        localStorage.setItem("ngoData", JSON.stringify(formData));
        
        toast({
          title: "NGO Created Successfully",
          description: "Welcome to your NGO management panel",
        });
        navigate("/dashboard");
      } else {
        toast({
          title: "Creation failed",
          description: "Please fill in all required fields",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    }, 1000);
  };

  const handleCancel = async () => {
    try {
      const result = await logout();
      if (result.success) {
        toast({
          title: "Logged out successfully",
          description: "You have been signed out",
        });
        navigate("/login");
      } else {
        toast({
          title: "Logout failed",
          description: result.error || "Failed to logout",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const categories = [
    "Health & Medical",
    "Education",
    "Environment",
    "Child Welfare",
    "Women Empowerment",
    "Disaster Relief",
    "Animal Welfare",
    "Community Development"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-muted to-accent-muted flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl shadow-strong relative">
        {/* X Button */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 h-8 w-8 p-0 rounded-full hover:bg-muted"
          onClick={handleCancel}
        >
          <X className="h-4 w-4" />
        </Button>
        
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 rounded-2xl flex items-center justify-center mb-4">
            {/* <Heart className="w-8 h-8 text-white" /> */}
            <img src="/daily-angel-logo-white.png" alt="NGO Logo" className="w-auto h-auto" />
          </div>
          <CardTitle className="text-2xl font-bold">Create Your NGO Profile</CardTitle>
          <CardDescription>
            Set up your organization to start managing causes and activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">NGO Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter your NGO name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Mission & Description *</Label>
              <Textarea
                id="description"
                placeholder="Describe your NGO's mission and goals..."
                className="min-h-[100px]"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact">Contact Information</Label>
                <Input
                  id="contact"
                  placeholder="Phone or email"
                  value={formData.contact}
                  onChange={(e) => setFormData({...formData, contact: e.target.value})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="taxId">Tax ID / Registration Number</Label>
                <Input
                  id="taxId"
                  placeholder="Legal registration ID"
                  value={formData.taxId}
                  onChange={(e) => setFormData({...formData, taxId: e.target.value})}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Logo Upload</Label>
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Drag and drop your logo here, or click to browse
                </p>
                <Button type="button" variant="outline" className="mt-2">
                  Choose File
                </Button>
              </div>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-gradient-to-r from-primary to-accent hover:opacity-90 transition-opacity"
              disabled={isLoading}
            >
              {isLoading ? "Creating NGO..." : "Create NGO & Continue"}
            </Button>
          </form>
          
          {/* Cancel Button */}
          <div className="mt-6 text-center">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleCancel}
              className="w-full"
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}