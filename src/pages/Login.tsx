import { useState } from "react";
import { <PERSON>, Eye, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login, user, userData, loading, checkUserHasNgo } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    if (!email || !password) {
      toast({
        title: "Login failed",
        description: "Please enter both email and password",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    try {
      const result = await login(email, password);
      
      if (result.success && result.user) {
        toast({
          title: "Login successful",
          description: "Welcome to Daily Angel NGO Panel",
        });
        
        // Check if user has an NGO in Firebase
        const hasNgo = await checkUserHasNgo(result.user.uid);
        
        // Conditional navigation based on NGO existence
        if (hasNgo) {
          navigate("/dashboard");
        } else {
          navigate("/create-ngo");
        }
      } else {
        toast({
          title: "Login failed",
          description: result.error || "Invalid credentials",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Login failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Redirect if user is already authenticated
  if (user && userData && !loading) {
    const hasNgo = userData.ngoId && userData.ngoId.trim() !== '';
    if (hasNgo) {
      navigate("/dashboard");
    } else {
      navigate("/create-ngo");
    }
    return null;
  }

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-muted to-accent-muted flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-muted to-accent-muted flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-strong">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center mb-4">
            <Heart className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold">Daily Angel</CardTitle>
          <CardDescription>
            Sign in to your NGO management panel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-gradient-to-r from-primary to-accent hover:opacity-90 transition-opacity"
              disabled={isLoading}
            >
              {isLoading ? "Signing in..." : "Sign in"}
            </Button>

            <div className="text-center space-y-2">
              <Button variant="link" className="text-sm text-muted-foreground">
                Forgot your password?
              </Button>
              <div className="text-sm text-muted-foreground">
                Don't have an account?{" "}
                <Button 
                  variant="link" 
                  className="p-0 h-auto text-sm"
                  onClick={() => navigate("/signup")}
                >
                  Sign up
                </Button>
              </div>
            </div>

            {/* Demo Helper - Only show when user is logged in */}
            {user && (
              <div className="pt-4 border-t border-border">
                <p className="text-xs text-muted-foreground text-center mb-2">
                  Demo: Current user data from Firebase
                </p>
                <div className="text-xs text-muted-foreground text-center mb-2">
                  User ID: {user.uid}
                </div>
                <div className="text-xs text-muted-foreground text-center mb-2">
                  NGO ID: {userData?.ngoId || "Not set"}
                </div>
                <div className="text-xs text-muted-foreground text-center">
                  Status: {userData?.ngoId && userData.ngoId.trim() !== '' ? "Has NGO" : "No NGO"}
                </div>
              </div>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}