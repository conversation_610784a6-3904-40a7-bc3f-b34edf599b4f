import { useState, useEffect } from "react";
import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged, 
  User,
  createUserWithEmailAndPassword
} from "firebase/auth";
import { auth } from "@/lib/firebase";
import { UserService, UserData } from "@/lib/user-service";

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);
      
      if (user) {
        try {
          // Check if user data exists in Firestore
          let userData = await UserService.getUserData(user.uid);
          
          // If user data doesn't exist, create it
          if (!userData) {
            userData = await UserService.createUserData(user);
          }
          
          setUserData(userData);
        } catch (error) {
          console.error("Error loading user data:", error);
        }
      } else {
        setUserData(null);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return { success: true, user: userCredential.user };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || "Login failed" 
      };
    }
  };

  const signup = async (email: string, password: string, userInfo?: { name: string; surname: string; photoUrl?: string }) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      
      // Create user data in Firestore with additional info
      const userData = await UserService.createUserData(userCredential.user, userInfo);
      
      return { success: true, user: userCredential.user, userData };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || "Signup failed" 
      };
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || "Logout failed" 
      };
    }
  };

  const checkUserHasNgo = async (userId: string): Promise<boolean> => {
    return await UserService.checkUserHasNgo(userId);
  };

  const updateUserNgoId = async (userId: string, ngoId: string): Promise<void> => {
    await UserService.updateUserNgoId(userId, ngoId);
    // Refresh user data after update
    if (user) {
      const updatedUserData = await UserService.getUserData(user.uid);
      setUserData(updatedUserData);
    }
  };

  return {
    user,
    userData,
    loading,
    login,
    signup,
    logout,
    checkUserHasNgo,
    updateUserNgoId,
  };
} 