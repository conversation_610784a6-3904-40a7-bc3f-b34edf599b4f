import { useQuery } from "@tanstack/react-query";
import { collection, doc, getDoc, getDocs, getFirestore, orderBy, query, where, limit as qlimit } from "firebase/firestore";
import type { CampaignDoc, CauseDoc, DonationDoc, NGODoc, UserDoc } from "../lib/types";
import { CategoryService, type Category } from "@/lib/category-service";

const db = getFirestore();

export function useNGOsByCategoryVerified(category: string, pageSize = 20) {
  return useQuery({
    queryKey: ["ngos", "category", category, pageSize],
    queryFn: async (): Promise<NGODoc[]> => {
      const q = query(
        collection(db, "ngos"),
        where("categories", "array-contains", category),
        where("isVerified", "==", true),
        qlimit(pageSize)
      );
      const snap = await getDocs(q);
      return snap.docs.map((d) => d.data() as NGODoc);
    },
  });
}

export function useNGOsByCountrySortedByTotalReceived(country: string, pageSize = 20) {
  return useQuery({
    queryKey: ["ngos", "country", country, pageSize],
    queryFn: async (): Promise<NGODoc[]> => {
      const q = query(
        collection(db, "ngos"),
        where("location.country", "==", country),
        orderBy("totalReceived", "desc"),
        qlimit(pageSize)
      );
      const snap = await getDocs(q);
      return snap.docs.map((d) => d.data() as NGODoc);
    },
  });
}

export function useCausesByCategoryStatus(category: string, status: CauseDoc["status"], pageSize = 20) {
  return useQuery({
    queryKey: ["causes", "category_status", category, status, pageSize],
    queryFn: async (): Promise<CauseDoc[]> => {
      const q = query(
        collection(db, "causes"),
        where("category", "==", category),
        where("status", "==", status),
        orderBy("endDate", "desc"),
        qlimit(pageSize)
      );
      const snap = await getDocs(q);
      return snap.docs.map((d) => d.data() as CauseDoc);
    },
  });
}

export function useCausesByCountrySortedByGoal(country: string, pageSize = 20) {
  return useQuery({
    queryKey: ["causes", "country_goal", country, pageSize],
    queryFn: async (): Promise<CauseDoc[]> => {
      const q = query(
        collection(db, "causes"),
        where("location.country", "==", country),
        orderBy("goalAmount", "desc"),
        qlimit(pageSize)
      );
      const snap = await getDocs(q);
      return snap.docs.map((d) => d.data() as CauseDoc);
    },
  });
}

export function useDonationsByDonor(donorId: string, pageSize = 20) {
  return useQuery({
    queryKey: ["donations", "byDonor", donorId, pageSize],
    queryFn: async (): Promise<DonationDoc[]> => {
      const q = query(
        collection(db, "donations"),
        where("donorId", "==", donorId),
        orderBy("createdAt", "desc"),
        qlimit(pageSize)
      );
      const snap = await getDocs(q);
      return snap.docs.map((d) => d.data() as DonationDoc);
    },
    enabled: Boolean(donorId),
  });
}

export function useDonationsByRecipientStatus(recipientId: string, status: DonationDoc["status"], pageSize = 20) {
  return useQuery({
    queryKey: ["donations", "byRecipient", recipientId, status, pageSize],
    queryFn: async (): Promise<DonationDoc[]> => {
      const q = query(
        collection(db, "donations"),
        where("recipientId", "==", recipientId),
        where("status", "==", status),
        orderBy("createdAt", "desc"),
        qlimit(pageSize)
      );
      const snap = await getDocs(q);
      return snap.docs.map((d) => d.data() as DonationDoc);
    },
    enabled: Boolean(recipientId && status),
  });
}

export function useUserById(userId: string) {
  return useQuery({
    queryKey: ["users", userId],
    queryFn: async (): Promise<UserDoc | null> => {
      const ref = doc(db, "users", userId);
      const snap = await getDoc(ref);
      return snap.exists() ? (snap.data() as UserDoc) : null;
    },
    enabled: Boolean(userId),
  });
}

export function useCategoriesActive() {
  return useQuery({
    queryKey: ["categories", "active"],
    queryFn: async (): Promise<Category[]> => {
      return CategoryService.getActiveCategories();
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
    retry: 2,
  });
}




