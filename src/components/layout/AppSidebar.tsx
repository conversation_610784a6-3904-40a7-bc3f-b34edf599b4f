import { useState } from "react";
import { 
  Home, 
  Heart, 
  Calendar, 
  DollarSign, 
  Building2, 
  Bell, 
  Settings, 
  LogOut,
  Menu,
  X
} from "lucide-react";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { useToast } from "@/hooks/use-toast";

const menuItems = [
  { title: "Dashboard", url: "/dashboard", icon: Home },
  { title: "Causes", url: "/causes", icon: Heart },
  { title: "Activities", url: "/activities", icon: Calendar },
  { title: "Donations", url: "/donations", icon: DollarSign },
  { title: "NGO Profile", url: "/profile", icon: Building2 },
  { title: "Notifications", url: "/notifications", icon: Bell },
  { title: "Settings", url: "/settings", icon: Settings },
];

export function AppSidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuthContext();
  const { toast } = useToast();

  const isActive = (path: string) => {
    if (path === "/dashboard") return location.pathname === "/dashboard";
    return location.pathname.startsWith(path);
  };

  return (
    <div className={cn(
      "h-screen bg-card border-r border-border flex flex-col transition-all duration-300",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="p-4 border-b border-border flex items-center justify-between">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
              <Heart className="w-4 h-4 text-white" />
            </div>
            <span className="font-semibold text-foreground">Daily Angel</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCollapsed(!collapsed)}
          className="h-8 w-8 p-0"
        >
          {collapsed ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.url}>
              <NavLink
                to={item.url}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors",
                  "hover:bg-primary-muted hover:text-primary",
                  isActive(item.url) 
                    ? "bg-primary text-primary-foreground shadow-sm" 
                    : "text-muted-foreground"
                )}
              >
                <item.icon className={cn("w-5 h-5", collapsed ? "mx-auto" : "")} />
                {!collapsed && <span className="font-medium">{item.title}</span>}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        {!collapsed && user && (
          <div className="mb-3 p-2 bg-muted rounded-lg">
            <p className="text-sm font-medium text-foreground truncate">
              {user.email}
            </p>
          </div>
        )}
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start text-muted-foreground hover:text-foreground hover:bg-muted",
            collapsed ? "px-0 justify-center" : "px-3"
          )}
          onClick={async () => {
            try {
              const result = await logout();
              if (result.success) {
                toast({
                  title: "Logged out successfully",
                  description: "You have been signed out",
                });
                navigate("/login");
              } else {
                toast({
                  title: "Logout failed",
                  description: result.error || "Failed to logout",
                  variant: "destructive",
                });
              }
            } catch (error) {
              toast({
                title: "Logout failed",
                description: "An unexpected error occurred",
                variant: "destructive",
              });
            }
          }}
        >
          <LogOut className={cn("w-5 h-5", collapsed ? "" : "mr-3")} />
          {!collapsed && <span>Logout</span>}
        </Button>
      </div>
    </div>
  );
}