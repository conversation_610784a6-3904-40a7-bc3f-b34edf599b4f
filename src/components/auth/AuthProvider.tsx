import { createContext, useContext, ReactNode } from "react";
import { useAuth } from "@/hooks/use-auth";
import { User } from "firebase/auth";
import { UserData } from "@/lib/user-service";

interface AuthContextType {
  user: User | null;
  userData: UserData | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; user?: User; error?: string }>;
  signup: (email: string, password: string, userInfo?: { name: string; surname: string; photoUrl?: string }) => Promise<{ success: boolean; user?: User; userData?: UserData; error?: string }>;
  logout: () => Promise<{ success: boolean; error?: string }>;
  checkUserHasNgo: (userId: string) => Promise<boolean>;
  updateUserNgoId: (userId: string, ngoId: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const auth = useAuth();

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
} 