{
  // Example:
  //
  // "indexes": [
  //   {
  //     "collectionGroup": "widgets",
  //     "queryScope": "COLLECTION",
  //     "fields": [
  //       { "fieldPath": "foo", "arrayConfig": "CONTAINS" },
  //       { "fieldPath": "bar", "mode": "DESCENDING" }
  //     ]
  //   },
  //
  //  "fieldOverrides": [
  //    {
  //      "collectionGroup": "widgets",
  //      "fieldPath": "baz",
  //      "indexes": [
  //        { "order": "ASCENDING", "queryScope": "COLLECTION" }
  //      ]
  //    },
  //   ]
  // ]
  "indexes": [
    {
      "collectionGroup": "ngos",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "categories", "arrayConfig": "CONTAINS" },
        { "fieldPath": "isVerified", "mode": "ASCENDING" }
      ]
    },
    {
      "collectionGroup": "ngos",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "location.country", "mode": "ASCENDING" },
        { "fieldPath": "totalReceived", "mode": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "causes",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "category", "mode": "ASCENDING" },
        { "fieldPath": "status", "mode": "ASCENDING" },
        { "fieldPath": "endDate", "mode": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "causes",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "location.country", "mode": "ASCENDING" },
        { "fieldPath": "goalAmount", "mode": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "donations",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "donorId", "mode": "ASCENDING" },
        { "fieldPath": "createdAt", "mode": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "donations",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "recipientId", "mode": "ASCENDING" },
        { "fieldPath": "status", "mode": "ASCENDING" },
        { "fieldPath": "createdAt", "mode": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "location.country", "mode": "ASCENDING" },
        { "fieldPath": "interests", "arrayConfig": "CONTAINS" }
      ]
    }
  ],
  "fieldOverrides": []
}